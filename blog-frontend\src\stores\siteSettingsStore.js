import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { siteSettingsApi } from '@/api'

/**
 * 系统设置状态管理
 */
export const useSiteSettingsStore = defineStore('siteSettings', () => {
  // 状态
  const settings = ref({
    site_title: 'BrownLu的博客',
    site_subtitle: '与你共享美好生活',
    nav_text: {
      Home: '首页',
      ArticleList: '文章',
      CategoryList: '分类',
      MemoList: '备忘录',
      About: '关于',
      Login: '登录'
    },
    nav_visible: {
      Home: true,
      ArticleList: true,
      CategoryList: true,
      MemoList: true,
      About: true,
      Login: true
    },
    footer_text: '© 2024 BrownLu的博客 - 保留所有权利',
    banner_image: '',
    logo_image: '',
    favicon: '',
    meta_description: 'BrownLu的个人博客，分享技术、生活和思考',
    meta_keywords: '博客,技术,编程,生活',
    custom_css: '',
    custom_js: '',
    show_runtime: true,
    site_start_date: new Date().toISOString(),
    comment_ai_review: true,
    comment_review_all: false,
    comment_review_api_key: '',
    email_enabled: false,
    email_api_key: '',
    friend_links: {}
  })

  const loading = ref(false)
  const error = ref(null)

  // 计算属性
  const siteTitle = computed(() => settings.value.site_title)
  const siteSubtitle = computed(() => settings.value.site_subtitle)
  const navText = computed(() => settings.value.nav_text || {})
  const navVisible = computed(() => settings.value.nav_visible || {})
  const footerText = computed(() => settings.value.footer_text)
  const bannerImage = computed(() => settings.value.banner_image)
  const logoImage = computed(() => settings.value.logo_image)
  const favicon = computed(() => settings.value.favicon)
  const metaDescription = computed(() => settings.value.meta_description)
  const metaKeywords = computed(() => settings.value.meta_keywords)
  const customCss = computed(() => settings.value.custom_css)
  const customJs = computed(() => settings.value.custom_js)
  const showRuntime = computed(() => settings.value.show_runtime)
  const siteStartDate = computed(() => {
    if (!settings.value.site_start_date) {
      console.log('站点设置中没有开始日期，使用当前日期')
      return new Date()
    }

    try {
      const date = new Date(settings.value.site_start_date)
      console.log('站点开始日期:', settings.value.site_start_date)
      console.log('转换后的日期对象:', date.toISOString())
      return date
    } catch (error) {
      console.error('日期转换错误:', error)
      return new Date()
    }
  })
  const commentAiReview = computed(() => settings.value.comment_ai_review)
  const commentReviewAll = computed(() => settings.value.comment_review_all)
  const commentReviewApiKey = computed(() => settings.value.comment_review_api_key)
  const emailEnabled = computed(() => settings.value.email_enabled)
  const emailApiKey = computed(() => settings.value.email_api_key)
  const friendLinks = computed(() => settings.value.friend_links || {})

  // 方法
  /**
   * 获取系统设置
   */
  const fetchSettings = async () => {
    loading.value = true
    error.value = null

    try {
      const response = await siteSettingsApi.getSiteSettings()
      console.log('从后端获取的设置数据:', response)

      // 确保 nav_visible 字段存在
      if (!response.nav_visible) {
        response.nav_visible = settings.value.nav_visible
        console.log('后端未返回导航显示控制设置，使用默认值')
      }

      // 确保 friend_links 字段存在
      if (!response.friend_links) {
        response.friend_links = settings.value.friend_links
        console.log('后端未返回友链设置，使用默认值')
      } else {
        console.log('后端返回的友链设置:', response.friend_links)
      }

      settings.value = { ...settings.value, ...response }
      console.log('更新后的设置数据:', settings.value)

      // 更新网站标题
      updateDocumentTitle()

      // 更新网站图标
      updateFavicon()

      // 更新自定义样式和脚本
      updateCustomCode()

      return response
    } catch (err) {
      console.error('获取系统设置失败:', err)
      error.value = '获取系统设置失败'
      throw err
    } finally {
      loading.value = false
    }
  }

  /**
   * 更新系统设置
   * @param {Object} data 设置数据
   */
  const updateSettings = async (data) => {
    loading.value = true
    error.value = null

    try {
      console.log('更新设置，提交的数据:', data)
      const response = await siteSettingsApi.updateSiteSettings(data)
      console.log('更新设置，后端返回的数据:', response)

      // 确保 nav_visible 字段存在
      if (!response.nav_visible && data.nav_visible) {
        response.nav_visible = data.nav_visible
        console.log('后端未返回导航显示控制设置，使用提交的值')
      }

      // 确保 friend_links 字段存在
      if (!response.friend_links && data.friend_links) {
        response.friend_links = data.friend_links
        console.log('后端未返回友链设置，使用提交的值')
      }

      settings.value = { ...settings.value, ...response }
      console.log('更新后的设置数据:', settings.value)

      // 更新网站标题
      updateDocumentTitle()

      // 更新网站图标
      updateFavicon()

      // 更新自定义样式和脚本
      updateCustomCode()

      return response
    } catch (err) {
      console.error('更新系统设置失败:', err)
      error.value = '更新系统设置失败'
      throw err
    } finally {
      loading.value = false
    }
  }

  /**
   * 更新文档标题
   */
  const updateDocumentTitle = () => {
    if (settings.value.site_title) {
      document.title = settings.value.site_subtitle
        ? `${settings.value.site_title} - ${settings.value.site_subtitle}`
        : settings.value.site_title
    }
  }

  /**
   * 更新网站图标
   */
  const updateFavicon = () => {
    if (settings.value.favicon) {
      const link = document.querySelector('link[rel="icon"]') || document.createElement('link')
      link.type = 'image/x-icon'
      link.rel = 'icon'
      link.href = settings.value.favicon

      if (!document.querySelector('link[rel="icon"]')) {
        document.head.appendChild(link)
      }
    }
  }

  /**
   * 更新自定义代码
   */
  const updateCustomCode = () => {
    // 更新自定义CSS
    let styleElement = document.getElementById('custom-css')
    if (!styleElement && settings.value.custom_css) {
      styleElement = document.createElement('style')
      styleElement.id = 'custom-css'
      document.head.appendChild(styleElement)
    }

    if (styleElement) {
      styleElement.textContent = settings.value.custom_css || ''
    }

    // 更新自定义JavaScript
    let scriptElement = document.getElementById('custom-js')
    if (scriptElement) {
      document.head.removeChild(scriptElement)
    }

    if (settings.value.custom_js) {
      scriptElement = document.createElement('script')
      scriptElement.id = 'custom-js'
      scriptElement.textContent = settings.value.custom_js
      document.head.appendChild(scriptElement)
    }
  }

  /**
   * 初始化系统设置
   */
  const initSettings = async () => {
    console.log('开始初始化系统设置')
    try {
      await fetchSettings()
      console.log('系统设置初始化完成，友链数据:', settings.value.friend_links)
    } catch (err) {
      console.error('初始化系统设置失败:', err)
    }
  }

  return {
    // 状态
    settings,
    loading,
    error,

    // 计算属性
    siteTitle,
    siteSubtitle,
    navText,
    navVisible,
    footerText,
    bannerImage,
    logoImage,
    favicon,
    metaDescription,
    metaKeywords,
    customCss,
    customJs,
    showRuntime,
    siteStartDate,
    commentAiReview,
    commentReviewAll,
    commentReviewApiKey,
    emailEnabled,
    emailApiKey,
    friendLinks,

    // 方法
    fetchSettings,
    updateSettings,
    initSettings
  }
})
