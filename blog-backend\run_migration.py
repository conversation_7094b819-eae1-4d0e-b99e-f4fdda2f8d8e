"""
执行数据库迁移：为 files 表添加 is_public 字段
"""
import os
import sys
import pymysql

def run_migration():
    """执行迁移"""
    try:
        # 使用.env.development中的数据库连接信息
        db_host = "*************"
        db_port = 7001
        db_user = "root"
        db_password = "lubkrf"
        db_name = "blog"

        print(f"连接到数据库: {db_host}:{db_port}/{db_name}")

        # 连接到数据库
        conn = pymysql.connect(
            host=db_host,
            port=db_port,
            user=db_user,
            password=db_password,
            database=db_name
        )

        try:
            with conn.cursor() as cursor:
                # 检查字段是否已存在
                cursor.execute("""
                    SELECT COUNT(*)
                    FROM information_schema.COLUMNS
                    WHERE TABLE_SCHEMA = %s
                    AND TABLE_NAME = 'files'
                    AND COLUMN_NAME = 'is_public'
                """, (db_name,))

                if cursor.fetchone()[0] > 0:
                    print("is_public 字段已存在，无需迁移")
                    return

                # 添加 is_public 字段
                print("正在添加 is_public 字段...")
                cursor.execute("ALTER TABLE files ADD COLUMN is_public BOOLEAN DEFAULT FALSE")
                conn.commit()
                print("成功添加 is_public 字段到文件表")

        finally:
            conn.close()

    except Exception as e:
        print(f"迁移失败: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    run_migration()
