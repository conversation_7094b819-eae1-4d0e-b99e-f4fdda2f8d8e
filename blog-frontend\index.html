<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>BrownLu的博客 - 与你共享美好生活</title>

  <!-- 添加多种尺寸的 favicon -->
  <link rel="icon" type="image/x-icon" href="/favicon.ico">
  <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png">
  <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png">
  <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png">
  <link rel="manifest" href="/site.webmanifest">

  <!-- 确保黑暗模式下的背景颜色是深蓝黑色 -->
  <style>
    html.dark {
      background-color: #111827 !important;
    }
    html.dark body {
      background-color: #111827 !important;
    }
    html.dark .bg-dark-primary {
      background-color: #111827 !important;
    }
  </style>
</head>
<body>
  <div id="app"></div>
  <script type="module" src="/src/main.js"></script>
</body>
</html>
