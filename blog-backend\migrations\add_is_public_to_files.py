"""
迁移脚本：为文件表添加 is_public 字段
"""
from sqlalchemy import create_engine, Column, Boolean, MetaData, Table
from app.core.config import settings

def run_migration():
    """
    执行迁移：为文件表添加 is_public 字段
    """
    # 创建数据库引擎
    engine = create_engine(settings.DATABASE_URL)
    
    # 创建元数据对象
    metadata = MetaData()
    
    # 反射现有表
    metadata.reflect(bind=engine)
    
    # 获取文件表
    files_table = metadata.tables.get('files')
    
    # 检查表是否存在
    if not files_table:
        print("文件表不存在，无需迁移")
        return
    
    # 检查字段是否已存在
    if 'is_public' in files_table.columns:
        print("is_public 字段已存在，无需迁移")
        return
    
    # 添加 is_public 字段
    try:
        with engine.begin() as conn:
            conn.execute(f"ALTER TABLE files ADD COLUMN is_public BOOLEAN DEFAULT FALSE")
        print("成功添加 is_public 字段到文件表")
    except Exception as e:
        print(f"迁移失败: {str(e)}")
        raise

if __name__ == "__main__":
    run_migration()
