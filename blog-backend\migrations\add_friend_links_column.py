"""
添加友情链接字段的数据库迁移脚本
"""
import os
import sys
import json
from datetime import datetime, timezone
from sqlalchemy import create_engine, Column, JSON, text
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# 导入数据库配置
from app.core.config import settings

# 创建数据库连接
DATABASE_URL = settings.DATABASE_URL
engine = create_engine(DATABASE_URL)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# 创建基础模型
Base = declarative_base()

def run_migration():
    """执行迁移操作"""
    session = SessionLocal()

    try:
        # 检查数据库类型
        db_type = engine.dialect.name
        print(f"数据库类型: {db_type}")

        # 检查friend_links列是否已存在
        if db_type == 'sqlite':
            # SQLite
            result = session.execute(text("PRAGMA table_info(site_settings)"))
            columns = [row[1] for row in result.fetchall()]

            if 'friend_links' not in columns:
                print("添加friend_links列...")
                # 添加friend_links列
                session.execute(text("ALTER TABLE site_settings ADD COLUMN friend_links JSON"))

                # 更新现有记录，设置默认值
                session.execute(text("UPDATE site_settings SET friend_links = '{}'"))

                print("friend_links列添加成功")
            else:
                print("friend_links列已存在，无需添加")

        elif db_type == 'mysql':
            # MySQL
            # 检查列是否存在
            result = session.execute(text("""
                SELECT COLUMN_NAME
                FROM INFORMATION_SCHEMA.COLUMNS
                WHERE TABLE_SCHEMA = DATABASE()
                AND TABLE_NAME = 'site_settings'
                AND COLUMN_NAME = 'friend_links'
            """))

            if not result.fetchone():
                print("添加friend_links列...")
                # 添加friend_links列
                session.execute(text("ALTER TABLE site_settings ADD COLUMN friend_links JSON"))

                # 更新现有记录，设置默认值
                session.execute(text("UPDATE site_settings SET friend_links = '{}'"))

                print("friend_links列添加成功")
            else:
                print("friend_links列已存在，无需添加")

        else:
            print(f"不支持的数据库类型: {db_type}")
            return

        session.commit()
        print("迁移完成")

    except Exception as e:
        session.rollback()
        print(f"迁移失败: {e}")
        raise
    finally:
        session.close()

if __name__ == "__main__":
    run_migration()
