from datetime import datetime
from typing import Optional, Dict, Any, List
from pydantic import BaseModel, Field

class SiteSettingsBase(BaseModel):
    """系统设置基础模式"""
    site_title: Optional[str] = Field(None, description="网站标题")
    site_subtitle: Optional[str] = Field(None, description="网站副标题")
    nav_text: Optional[Dict[str, str]] = Field(default_factory=dict, description="导航栏文字，键为路由名称，值为显示文字")
    nav_visible: Optional[Dict[str, bool]] = Field(default_factory=dict, description="导航栏项目显示控制，键为路由名称，值为是否显示")
    banner_image: Optional[str] = Field(None, description="首页banner图片URL")
    footer_text: Optional[str] = Field(None, description="页脚文字")
    logo_image: Optional[str] = Field(None, description="网站logo图片URL")
    favicon: Optional[str] = Field(None, description="网站favicon图标URL")
    meta_description: Optional[str] = Field(None, description="网站元描述")
    meta_keywords: Optional[str] = Field(None, description="网站元关键词")
    custom_css: Optional[str] = Field(None, description="自定义CSS")
    custom_js: Optional[str] = Field(None, description="自定义JavaScript")
    require_email_verification: Optional[bool] = Field(False, description="是否要求邮箱验证")
    show_runtime: Optional[bool] = Field(True, description="是否显示网站运行时长")
    site_start_date: Optional[datetime] = Field(None, description="网站创建日期")
    comment_ai_review: Optional[bool] = Field(True, description="是否使用AI审核评论")
    comment_review_all: Optional[bool] = Field(False, description="是否审核所有评论（包括已登录用户）")
    comment_review_api_key: Optional[str] = Field(None, description="评论审核API密钥")
    email_enabled: Optional[bool] = Field(False, description="是否启用邮件功能")
    email_api_key: Optional[str] = Field(None, description="Resend API密钥")
    friend_links: Optional[Dict[str, str]] = Field(default_factory=dict, description="友情链接，键为网站名称，值为URL")

class SiteSettingsCreate(SiteSettingsBase):
    """创建系统设置模式"""
    site_title: str = Field(..., description="网站标题")

class SiteSettingsUpdate(SiteSettingsBase):
    """更新系统设置模式"""
    pass

class SiteSettingsResponse(SiteSettingsBase):
    """系统设置响应模式"""
    id: int
    created_at: datetime
    updated_at: datetime

    model_config = {
        "from_attributes": True
    }

    @classmethod
    def model_validate(cls, obj, *args, **kwargs):
        import json

        # 如果 nav_text 是字符串，尝试将其解析为 JSON
        if hasattr(obj, 'nav_text'):
            if isinstance(obj.nav_text, str) and obj.nav_text:
                try:
                    obj.nav_text = json.loads(obj.nav_text)
                except json.JSONDecodeError:
                    obj.nav_text = {}
            elif obj.nav_text is None:
                obj.nav_text = {}

        # 如果 nav_visible 是字符串，尝试将其解析为 JSON
        if hasattr(obj, 'nav_visible'):
            if isinstance(obj.nav_visible, str) and obj.nav_visible:
                try:
                    obj.nav_visible = json.loads(obj.nav_visible)
                except json.JSONDecodeError:
                    obj.nav_visible = {}
            elif obj.nav_visible is None:
                obj.nav_visible = {}

        # 如果 friend_links 是字符串，尝试将其解析为 JSON
        if hasattr(obj, 'friend_links'):
            if isinstance(obj.friend_links, str) and obj.friend_links:
                try:
                    obj.friend_links = json.loads(obj.friend_links)
                except json.JSONDecodeError:
                    obj.friend_links = {}
            elif obj.friend_links is None:
                obj.friend_links = {}

        return super().model_validate(obj, *args, **kwargs)

class PublicSiteSettingsResponse(BaseModel):
    """公开的系统设置响应模式，不包含敏感信息"""
    id: int
    site_title: Optional[str] = None
    site_subtitle: Optional[str] = None
    nav_text: Dict[str, str] = Field(default_factory=dict)
    nav_visible: Dict[str, bool] = Field(default_factory=dict)
    banner_image: Optional[str] = None
    footer_text: Optional[str] = None
    logo_image: Optional[str] = None
    favicon: Optional[str] = None
    meta_description: Optional[str] = None
    meta_keywords: Optional[str] = None
    custom_css: Optional[str] = None
    custom_js: Optional[str] = None
    require_email_verification: bool = False
    show_runtime: bool = True
    site_start_date: Optional[datetime] = None
    comment_ai_review: bool = True
    comment_review_all: bool = False
    email_enabled: bool = False
    friend_links: Dict[str, str] = Field(default_factory=dict)
    created_at: datetime
    updated_at: datetime

    model_config = {
        "from_attributes": True
    }

    @classmethod
    def from_settings(cls, settings):
        """从完整设置创建公开设置对象"""
        return cls(
            id=settings.id,
            site_title=settings.site_title,
            site_subtitle=settings.site_subtitle,
            nav_text=settings.nav_text if isinstance(settings.nav_text, dict) else {},
            nav_visible=settings.nav_visible if isinstance(settings.nav_visible, dict) else {},
            banner_image=settings.banner_image,
            footer_text=settings.footer_text,
            logo_image=settings.logo_image,
            favicon=settings.favicon,
            meta_description=settings.meta_description,
            meta_keywords=settings.meta_keywords,
            custom_css=settings.custom_css,
            custom_js=settings.custom_js,
            require_email_verification=settings.require_email_verification,
            show_runtime=settings.show_runtime,
            site_start_date=settings.site_start_date,
            comment_ai_review=settings.comment_ai_review,
            comment_review_all=settings.comment_review_all,
            email_enabled=settings.email_enabled,
            friend_links=settings.friend_links if isinstance(settings.friend_links, dict) else {},
            created_at=settings.created_at,
            updated_at=settings.updated_at
        )
