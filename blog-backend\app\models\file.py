from sqlalchemy import Column, Integer, String, DateTime, Foreign<PERSON><PERSON>, Boolean
from sqlalchemy.orm import relationship
from datetime import datetime, timezone

from app.core.database import Base

class File(Base):
    """文件模型，用于存储上传的文件信息"""

    __tablename__ = "files"

    id = Column(Integer, primary_key=True, index=True)
    filename = Column(String(255), nullable=False)
    original_filename = Column(String(255), nullable=False)
    file_path = Column(String(255), nullable=False)
    file_type = Column(String(50), nullable=False)
    file_size = Column(Integer, nullable=False)  # 文件大小（字节）
    mime_type = Column(String(100), nullable=True)
    uploaded_by = Column(Integer, ForeignKey("users.id"))
    created_at = Column(DateTime, default=lambda: datetime.now(timezone.utc))
    updated_at = Column(DateTime, default=lambda: datetime.now(timezone.utc), onupdate=lambda: datetime.now(timezone.utc))
    is_public = Column(Boolean, default=False)  # 是否允许未登录用户下载

    # 关系
    user = relationship("User", back_populates="files")

    def __repr__(self):
        return f"<File {self.filename}>"
