{"name": "blog-hover-animation", "version": "0.1.0", "private": true, "scripts": {"dev": "vite --mode development", "build": "vite build --mode production", "preview": "vite preview", "build:dev": "vite build --mode development"}, "dependencies": {"@number-flow/vue": "^0.4.7", "@tailwindcss/typography": "^0.5.16", "@vueuse/core": "^13.1.0", "animate.css": "^4.1.1", "ant-design-vue": "^4.2.6", "axios": "^1.8.4", "canvas-confetti": "^1.9.3", "chart.js": "^4.4.8", "clsx": "^2.1.1", "date-fns": "^4.1.0", "gsap": "^3.12.5", "highlight.js": "^11.11.1", "katex": "^0.16.22", "lenis": "^1.1.20", "markdown-it": "^14.1.0", "markdown-it-katex": "^2.0.3", "markdown-it-task-lists": "^2.1.1", "motion-v": "^1.0.0-beta.2", "number-flow": "^0.5.7", "pinia": "^3.0.2", "prismjs": "^1.30.0", "tailwind-merge": "^3.2.0", "terser": "^5.39.0", "v-tooltip": "^2.1.3", "vditor": "^3.10.9", "vue": "^3.3.4", "vue-calendar-heatmap": "^0.8.4", "vue-router": "^4.5.0", "vuewordcloud": "^19.0.0"}, "devDependencies": {"@tailwindcss/postcss7-compat": "^2.2.17", "@vitejs/plugin-vue": "^4.2.3", "autoprefixer": "^10.4.21", "postcss": "^8.4.31", "postcss-import": "^16.1.0", "tailwindcss": "^3.3.0", "tippy.js": "^6.3.7", "vite": "^4.4.5", "vue3-calendar-heatmap": "^2.0.5"}}