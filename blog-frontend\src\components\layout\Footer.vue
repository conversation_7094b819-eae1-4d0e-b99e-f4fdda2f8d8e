<template>
  <footer class="bg-secondary dark:bg-dark-primary text-primary dark:text-dark-secondary py-8 px-8 mt-auto">
    <div class="container mx-auto">
      <!-- 顶部区域：网站信息、导航链接和联系方式 -->
      <div class="flex flex-col md:flex-row justify-between">
        <!-- 左侧：网站信息和友情链接 -->
        <div class="md:w-1/3 mb-6 md:mb-0">
          <div class="mb-6">
            <h3 class="text-xl font-bold uppercase">{{ siteTitle }}</h3>
            <p class="text-sm mt-2">{{ siteSubtitle }}</p>
          </div>

          <!-- 友情链接 -->
          <div class="mt-6">
            <h4 class="text-lg font-medium mb-3">友情链接</h4>
            <div class="flex flex-wrap gap-2">
              <template v-if="Object.keys(friendLinks).length > 0">
                <!-- 使用 entries 方法正确遍历对象 -->
                <a
                  v-for="[name, url] in Object.entries(friendLinks)"
                  :key="'link-' + name"
                  :href="formatUrl(url)"
                  target="_blank"
                  class="px-3 py-1 bg-gray-200 dark:bg-gray-700 rounded-full text-sm text-gray-800 dark:text-gray-200 hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors"
                >
                  {{ name }}
                </a>
              </template>
              <!-- 无友链时的提示 -->
              <div v-else class="text-xs text-gray-500">
                暂无友链
              </div>
            </div>
          </div>
        </div>

        <!-- 右侧：导航链接和联系方式 -->
        <div class="md:w-2/3 flex flex-col md:flex-row justify-end gap-8">
          <div>
            <h4 class="text-lg font-medium mb-2">链接</h4>
            <ul class="space-y-1">
              <li v-if="navVisible.Home !== false"><router-link to="/" class="text-primary dark:text-dark-secondary hover:text-gray-400 transition-colors">{{ navItems.Home || '首页' }}</router-link></li>
              <li v-if="navVisible.ArticleList !== false"><router-link to="/articles" class="text-primary dark:text-dark-secondary hover:text-gray-400 transition-colors">{{ navItems.ArticleList || '文章' }}</router-link></li>
              <li v-if="navVisible.CategoryList !== false"><router-link to="/categories" class="text-primary dark:text-dark-secondary hover:text-gray-400 transition-colors">{{ navItems.CategoryList || '分类' }}</router-link></li>
              <li v-if="navVisible.MemoList !== false"><router-link to="/memos" class="text-primary dark:text-dark-secondary hover:text-gray-400 transition-colors">{{ navItems.MemoList || '备忘录' }}</router-link></li>
              <li v-if="navVisible.About !== false"><router-link to="/about" class="text-primary dark:text-dark-secondary hover:text-gray-400 transition-colors">{{ navItems.About || '关于' }}</router-link></li>
            </ul>
          </div>

          <div>
            <h4 class="text-lg font-medium mb-2">联系我</h4>
            <ul class="space-y-1">
              <li><a href="mailto:<EMAIL>" class="text-primary dark:text-dark-secondary hover:text-gray-400 transition-colors">Email</a></li>
              <li><a href="https://github.com/brownlu" target="_blank" class="text-primary dark:text-dark-secondary hover:text-gray-400 transition-colors">GitHub</a></li>
              <li><a href="https://twitter.com/brownlu" target="_blank" class="text-primary dark:text-dark-secondary hover:text-gray-400 transition-colors">Twitter</a></li>
            </ul>
          </div>
        </div>
      </div>

      <!-- 底部版权信息 -->
      <div class="border-t border-gray-700 mt-8 pt-6 text-center text-sm">
        <p>{{ footerText }}</p>
        <p v-if="showRuntime" class="mt-2 text-xs text-gray-500 dark:text-gray-400">
          网站已运行 <span class="font-mono font-medium">{{ runtimeText }}</span>
        </p>
      </div>
    </div>
  </footer>
</template>

<script>
import { computed, ref, onMounted, onUnmounted } from 'vue'
import { useSiteSettingsStore } from '@/stores'
import { differenceInDays, differenceInYears, differenceInMonths } from 'date-fns'

export default {
  name: 'Footer',
  setup() {
    const siteSettingsStore = useSiteSettingsStore()

    // 获取网站设置
    const siteTitle = computed(() => siteSettingsStore.siteTitle || 'BrownLu的博客')
    const siteSubtitle = computed(() => siteSettingsStore.siteSubtitle || '与你共享美好生活')
    const footerText = computed(() => siteSettingsStore.footerText || `© ${new Date().getFullYear()} BrownLu的博客. 保留所有权利.`)
    const navItems = computed(() => siteSettingsStore.navText || {
      Home: '首页',
      ArticleList: '文章',
      CategoryList: '分类',
      MemoList: '备忘录',
      About: '关于'
    })
    // 导航显示控制
    const navVisible = computed(() => siteSettingsStore.navVisible || {
      Home: true,
      ArticleList: true,
      CategoryList: true,
      MemoList: true,
      About: true
    })

    // 友情链接相关
    const friendLinks = computed(() => siteSettingsStore.friendLinks)
    const hasFriendLinks = computed(() => Object.keys(friendLinks.value).length > 0)

    // 格式化URL，确保包含协议前缀
    const formatUrl = (url) => {
      if (!url) return '#'

      // 如果URL不包含协议前缀，添加https://
      if (!url.match(/^https?:\/\//i)) {
        return `https://${url}`
      }

      return url
    }

    // 网站运行时长相关
    const showRuntime = computed(() => siteSettingsStore.showRuntime)
    const siteStartDate = computed(() => {
      const date = siteSettingsStore.siteStartDate
      console.log('网站开始日期:', date, typeof date)
      return date
    })
    const runtimeText = ref('')
    let timer = null

    // 计算网站运行时长
    const calculateRuntime = () => {
      if (!siteStartDate.value) {
        console.error('网站开始日期为空')
        runtimeText.value = '未知'
        return
      }

      try {
        const now = new Date()
        console.log('当前日期:', now.toISOString())

        // 确保 siteStartDate.value 是 Date 对象
        let startDate
        if (siteStartDate.value instanceof Date) {
          startDate = siteStartDate.value
        } else {
          startDate = new Date(siteStartDate.value)
        }

        console.log('网站开始日期对象:', startDate.toISOString())

        // 检查日期是否有效
        if (isNaN(startDate.getTime())) {
          console.error('无效的网站创建日期:', siteStartDate.value)
          runtimeText.value = '未知'
          return
        }

        // 计算时间差（毫秒）
        const diffMs = now.getTime() - startDate.getTime()
        console.log('时间差（毫秒）:', diffMs)

        // 如果时间差为负数（未来日期），显示0
        if (diffMs < 0) {
          console.log('网站开始日期在未来，显示0')
          runtimeText.value = '0天0时0分0秒'
          return
        }

        // 计算年、月、日、时、分、秒
        const msPerSecond = 1000
        const msPerMinute = msPerSecond * 60
        const msPerHour = msPerMinute * 60
        const msPerDay = msPerHour * 24
        const msPerMonth = msPerDay * 30
        const msPerYear = msPerDay * 365

        const years = Math.floor(diffMs / msPerYear)
        const months = Math.floor((diffMs % msPerYear) / msPerMonth)
        const days = Math.floor((diffMs % msPerMonth) / msPerDay)
        const hours = Math.floor((diffMs % msPerDay) / msPerHour)
        const minutes = Math.floor((diffMs % msPerHour) / msPerMinute)
        const seconds = Math.floor((diffMs % msPerMinute) / msPerSecond)

        console.log('计算结果:', { years, months, days, hours, minutes, seconds })

        // 直接使用天、时、分、秒的格式
        // 为了简化显示，我们将年和月转换为天
        const totalDays = years * 365 + months * 30 + days

        // 构建显示文本
        const text = `${totalDays}天${hours}时${minutes}分${seconds}秒`

        runtimeText.value = text
      } catch (error) {
        console.error('计算网站运行时长出错:', error)
        runtimeText.value = '未知'
      }
    }

    // 生命周期钩子
    onMounted(() => {
      // 初始计算
      calculateRuntime()

      // 设置定时器，每秒更新一次
      timer = setInterval(calculateRuntime, 1000) // 1秒 = 1000毫秒
    })

    onUnmounted(() => {
      // 清除定时器
      if (timer) {
        clearInterval(timer)
      }
    })

    return {
      siteTitle,
      siteSubtitle,
      footerText,
      navItems,
      navVisible,
      showRuntime,
      runtimeText,
      friendLinks,
      hasFriendLinks,
      formatUrl
    }
  }
}
</script>

<style scoped>
/* 使用Tailwind类，无需额外样式 */
</style>
